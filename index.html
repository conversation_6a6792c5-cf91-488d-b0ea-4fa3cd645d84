<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title class="lang-cn hidden">GoMyHire 合作伙伴信用额度指南</title><title class="lang-en">GoMyHire Partner Credit Limit Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #b514e6cf, #f3c295cf);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            counter-reset: slide-counter;
        }

        .slide {
            background: white;
            margin: 10px 0;
            padding: 15px; /* 减小内边距 */
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            counter-increment: slide-counter;
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #b514e6cf, #f3c295cf);
        }

        .slide-number {
            position: absolute;
            top: 15px;
            right: 20px;
            background: #b514e6cf;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .slide-number::before {
            content: counter(slide-counter) "/10";
        }

        .slide h1 {
            color: #b514e6cf;
            font-size: 36px;
            margin-bottom: 10px;
            text-align: center;
        }

        .slide h2 {
            color: #b514e6cf;
            font-size: 26px;
            margin-bottom: 15px;
            border-bottom: 2px solid #f3c295cf;
            padding-bottom: 8px;
        }

        .slide h3 {
            color: #b514e6cf;
            font-size: 22px;
            margin: 15px 0 10px 0;
        }

        .cover-slide {
            background: linear-gradient(135deg, #b514e6cf, #8a0db8cf);
            color: white;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .cover-slide h1 {
            color: white;
            font-size: 48px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .cover-slide .subtitle {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cover-slide .date {
            font-size: 18px;
            opacity: 0.8;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }

        .settlement-table {
            width: 90%;
            margin: 15px auto;
            font-family: 'Courier New', Courier, monospace;
        }

        th {
            background: linear-gradient(135deg, #b514e6cf, #8a0db8cf);
            background-size: 100% 100%;
            background-repeat: no-repeat;
            color: white;
            padding: 12px 15px;
            text-align: left;
            font-weight: bold;
        }

        td {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
        }

        tr:nth-child(even) {
            background: #f9f9f9;
        }

        tr:hover {
            background: #f0f0f0;
            transition: background 0.3s ease;
        }

        .step-list {
            list-style: none;
            counter-reset: step-counter;
        }

        .step-list li {
            counter-increment: step-counter;
            margin: 10px 0;
            padding: 12px;
            background: #f8f9fa;
            border-left: 4px solid #b514e6cf;
            border-radius: 5px;
            position: relative;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: -15px;
            top: 15px;
            background: #b514e6cf;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .step-container {
            margin-bottom: 5px; /* 进一步减小外边距 */
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px; /* 减小内边距 */
            background: #fafafa;
        }

        .step-container table {
            margin-bottom: 15px;
        }





        .tip-box {
            background: linear-gradient(135deg, #fff3e0, #ffe0b2);
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #ff9800;
        }

        .tip-box::before {
            content: "💡 ";
            font-size: 18px;
        }

        .faq-item {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #b514e6cf;
        }

        .faq-question {
            font-weight: bold;
            color: #b514e6cf;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .faq-answer {
            color: #555;
            line-height: 1.6;
        }

        .contact-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }

        .contact-card {
            background: linear-gradient(135deg, #f3c295, #e8b87a);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            color: #333;
        }

        .contact-card h4 {
            color: #b514e6cf;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .promo-table {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        }

        .promo-table th {
            background: linear-gradient(135deg, #4caf50, #388e3c);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }

        /* 图片通用样式 */
        .slide-image {
            max-width: 100%;
            width: auto;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            object-fit: contain;
            margin: 10px 0;
        }

        .slide-image.with-margin-bottom {
            margin-bottom: 15px;
        }

        .slide-image.with-margin-left {
            margin-left: 20px;
        }

        /* 特殊样式元素 */
        .text-center-highlight {
            text-align: center;
            margin-top: 40px;
            font-size: 24px;
            color: #b514e6cf;
            font-weight: bold;
        }

        .tips-slide {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }

        .tips-title {
            color: #6c757d;
        }

        /* 字段介绍样式 */
        .field-introduction {
            margin: 15px 0;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .field-introduction h3 {
            color: #007bff;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .field-section {
            margin-bottom: 15px;
            padding: 12px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }

        .field-section h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

        .field-detail {
            margin-bottom: 8px;
            padding: 8px 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }

        .field-detail strong {
            color: #28a745;
            display: inline-block;
            min-width: 200px;
        }

        /* 需求部分样式 */
        .requirements-section {
            margin: 15px 0;
            padding: 12px;
            background-color: #fff3cd;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }

        .requirements-section h3 {
            color: #856404;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #ffc107;
            padding-bottom: 10px;
        }

        .requirement-category {
            margin-bottom: 15px;
            padding: 12px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }

        .requirement-category h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

        .requirement-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .requirement-list li {
            margin-bottom: 8px;
            padding: 8px 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #ffc107;
            position: relative;
            padding-left: 25px;
        }

        .requirement-list li:before {
            content: "▶";
            color: #ffc107;
            position: absolute;
            left: 10px;
            top: 10px;
        }

        .lang-en {
            transition: opacity 0.3s ease;
        }

        .lang-en.hidden {
            display: none;
        }

        .lang-cn {
            transition: opacity 0.3s ease;
        }

        .lang-cn.hidden {
            display: none;
        }

        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #b514e6, #f3c295);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(181, 20, 230, 0.3);
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .language-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(181, 20, 230, 0.4);
        }

        .language-toggle:active {
            transform: translateY(0);
        }

        .requirement-list li strong {
            color: #856404;
        }

        .navigation {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 40px;
            min-height: 40px;
        }

        .navigation.expanded {
            padding: 15px;
            min-width: 200px;
            min-height: auto;
        }

        .navigation-toggle {
            display: block;
            color: #b514e6cf;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 0;
            transition: all 0.3s ease;
        }

        .navigation.expanded .navigation-toggle {
            margin-bottom: 10px;
            text-align: left;
        }

        .navigation-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .navigation.expanded .navigation-content {
            max-height: 500px;
        }

        .navigation h4 {
            color: #b514e6cf;
            margin-bottom: 10px;
            font-size: 16px;
            display: none;
        }

        .navigation.expanded h4 {
            display: block;
        }

        .navigation a {
            display: none;
            color: #666;
            text-decoration: none;
            padding: 5px 0;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .navigation.expanded a {
            display: block;
        }

        .navigation a:hover {
            color: #b514e6cf;
        }

        /* 移动端优化样式 */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
                max-width: 100%;
            }
            
            .slide {
                padding: 15px;
                margin: 6px 0;
                border-radius: 10px;
            }
            
            /* 导航栏在手机上保持可见但调整大小 */
            .navigation {
                top: 10px;
                left: 10px;
                padding: 8px;
                min-width: 35px;
                min-height: 35px;
                border-radius: 8px;
            }
            
            .navigation.expanded {
                min-width: 180px;
                max-width: calc(100vw - 80px);
            }
            
            .navigation-toggle {
                font-size: 16px;
            }
            
            .navigation a {
                font-size: 13px;
                padding: 3px 0;
            }
            
            /* 语言切换按钮优化 */
            .language-toggle {
                top: 10px;
                right: 10px;
                padding: 8px 12px;
                font-size: 12px;
                border-radius: 20px;
            }
            
            /* 标题字体大小优化 */
            .slide h1 {
                font-size: 24px;
                line-height: 1.2;
                margin-bottom: 15px;
            }
            
            .slide h2 {
                font-size: 20px;
                line-height: 1.3;
                margin-bottom: 12px;
            }
            
            .slide h3 {
                font-size: 18px;
                margin: 12px 0 8px 0;
                line-height: 1.3;
            }
            
            /* 封面页优化 */
            .cover-slide h1 {
                font-size: 28px;
                margin-bottom: 15px;
            }
            
            .cover-slide .subtitle {
                font-size: 18px;
                margin-bottom: 20px;
            }
            
            .cover-slide .date {
                font-size: 14px;
            }
            
            /* 表格优化 - 方案一增强 */
            table {
                font-size: 14px;
                margin: 8px 0;
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .settlement-table {
                min-width: 600px; /* 确保表格最小宽度 */
                width: 100%;
            }

            th, td {
                padding: 8px 10px;
                word-wrap: break-word;
                -webkit-hyphens: auto;
                hyphens: auto;
                white-space: nowrap; /* 防止文字换行 */
            }
            
            /* 修复移动端表头背景色显示问题 */
            th {
                background: linear-gradient(135deg, #b514e6cf, #8a0db8cf) !important;
                background-size: 100% 100% !important;
                background-repeat: no-repeat !important;
                background-attachment: scroll !important;
                color: white !important;
                position: relative;
            }

            /* 确保表头在横向滚动时背景色正常显示 */
            .settlement-table th {
                background: linear-gradient(135deg, #b514e6cf, #8a0db8cf) !important;
                min-width: 80px; /* 确保表头最小宽度 */
            }

            /* 特殊表格的表头样式 */
            .promo-table th {
                background: linear-gradient(135deg, #4caf50, #388e3c) !important;
                background-size: 100% 100% !important;
                background-repeat: no-repeat !important;
            }

            /* 表格滚动提示 */
            .table-scroll-hint {
                text-align: center;
                font-size: 12px;
                color: #666;
                margin-top: 5px;
                opacity: 0.8;
            }

            /* 强制表格保持正确的表格布局 */
            table.settlement-table {
                display: table !important;
                table-layout: auto;
            }

            table.settlement-table thead,
            table.settlement-table tbody {
                display: table-header-group;
                display: table-row-group;
            }

            table.settlement-table tr {
                display: table-row;
            }

            table.settlement-table th,
            table.settlement-table td {
                display: table-cell;
            }

            /* 表格容器滚动 */
            .table-container {
                display: block;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            
            /* 步骤列表优化 */
            .step-list li {
                margin: 8px 0;
                padding: 10px;
                font-size: 14px;
                line-height: 1.4;
            }
            
            .step-list li::before {
                width: 25px;
                height: 25px;
                left: -12px;
                top: 12px;
                font-size: 12px;
            }
            
            /* 步骤容器优化 */
            .step-container {
                padding: 8px;
                margin-bottom: 4px;
            }
            
            /* 字段介绍优化 */
            .field-introduction {
                padding: 10px;
                margin: 12px 0;
            }
            
            .field-section {
                padding: 10px;
                margin-bottom: 12px;
            }
            
            .field-detail {
                padding: 6px 8px;
                margin-bottom: 6px;
                font-size: 14px;
                line-height: 1.4;
            }
            
            .field-detail strong {
                min-width: auto;
                display: block;
                margin-bottom: 2px;
            }
            
            /* 需求部分优化 */
            .requirements-section {
                padding: 10px;
                margin: 12px 0;
            }
            
            .requirement-category {
                padding: 10px;
                margin-bottom: 12px;
            }
            
            .requirement-list li {
                padding: 6px 8px 6px 20px;
                margin-bottom: 6px;
                font-size: 14px;
                line-height: 1.4;
            }
            
            /* 提示框优化 */
            .tip-box {
                padding: 10px;
                margin: 8px 0;
                font-size: 14px;
                line-height: 1.4;
            }
            
            /* FAQ 优化 */
            .faq-item {
                margin: 12px 0;
                padding: 12px;
            }
            
            .faq-question {
                font-size: 16px;
                margin-bottom: 8px;
                line-height: 1.3;
            }
            
            .faq-answer {
                font-size: 14px;
                line-height: 1.4;
            }
            
            /* 联系信息优化 */
            .contact-info {
                grid-template-columns: 1fr;
                gap: 12px;
                margin: 12px 0;
            }
            
            .contact-card {
                padding: 15px;
                text-align: left;
            }
            
            .contact-card h4 {
                font-size: 16px;
                margin-bottom: 8px;
            }
            
            /* 图片优化 */
            img {
                max-width: 100% !important;
                height: auto !important;
                margin: 10px 0 !important;
                border-radius: 6px !important;
            }

            /* 方案一：图片样式增强 */
            .slide-image {
                max-width: 100%;
                height: auto;
                border-radius: 8px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                margin: 15px 0;
                transition: opacity 0.3s ease;
            }

            .slide-image.with-margin-bottom {
                margin-bottom: 20px;
            }

            .slide-image.with-margin-left {
                margin-left: 0;
                margin-right: 0;
            }
            
            /* 幻灯片编号优化 */
            .slide-number {
                top: 10px;
                right: 15px;
                padding: 4px 10px;
                font-size: 12px;
            }
            
            /* 滚动优化 */
            html {
                scroll-behavior: smooth;
            }
            
            body {
                overflow-x: hidden;
            }
        }

        /* 超小屏幕优化 (小于480px) */
        @media (max-width: 480px) {
            .container {
                padding: 3px;
            }
            
            .slide {
                padding: 12px;
                margin: 3px 0;
            }
            
            .slide h1 {
                font-size: 20px;
            }
            
            .slide h2 {
                font-size: 18px;
            }
            
            .slide h3 {
                font-size: 16px;
            }
            
            .cover-slide h1 {
                font-size: 24px;
            }
            
            .cover-slide .subtitle {
                font-size: 16px;
            }
            
            .language-toggle {
                padding: 6px 10px;
                font-size: 11px;
            }
            
            .navigation {
                top: 8px;
                left: 8px;
                padding: 6px;
                min-width: 32px;
                min-height: 32px;
            }
            
            .navigation.expanded {
                min-width: 160px;
                max-width: calc(100vw - 60px);
            }
            
            table {
                font-size: 12px;
                margin: 6px 0;
            }
            
            th, td {
                padding: 6px 8px;
            }
            
            /* 超小屏幕表头背景色修复 */
            th {
                background: linear-gradient(135deg, #b514e6cf, #8a0db8cf) !important;
                background-size: 100% 100% !important;
                background-repeat: no-repeat !important;
                background-attachment: scroll !important;
                color: white !important;
            }

            .settlement-table th {
                background: linear-gradient(135deg, #b514e6cf, #8a0db8cf) !important;
                min-width: 60px; /* 超小屏幕的最小宽度 */
            }

            .promo-table th {
                background: linear-gradient(135deg, #4caf50, #388e3c) !important;
                background-size: 100% 100% !important;
                background-repeat: no-repeat !important;
            }
            
            .field-detail strong {
                font-size: 13px;
            }
            
            .step-list li {
                font-size: 13px;
                padding: 8px;
                margin: 6px 0;
            }
            
            .tip-box {
                font-size: 13px;
                padding: 8px;
                margin: 6px 0;
            }
            
            .faq-question {
                font-size: 14px;
            }
            
            .faq-answer {
                font-size: 13px;
            }
        }

        /* 方案一：移动端优化增强样式 */
        .mobile-optimized {
            -webkit-tap-highlight-color: rgba(181, 20, 230, 0.2);
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        .mobile-optimized .slide {
            touch-action: pan-y;
        }

        .mobile-optimized table {
            touch-action: pan-x;
        }

        /* 触摸反馈增强 */
        @media (max-width: 768px) {
            .faq-item:active,
            .contact-card:active,
            .tip-box:active {
                background-color: rgba(181, 20, 230, 0.05);
                transform: scale(0.99);
                transition: all 0.1s ease;
            }

            /* 表格滚动条样式优化 */
            table::-webkit-scrollbar {
                height: 6px;
            }

            table::-webkit-scrollbar-track {
                background: rgba(0,0,0,0.1);
                border-radius: 3px;
            }

            table::-webkit-scrollbar-thumb {
                background: rgba(181, 20, 230, 0.5);
                border-radius: 3px;
            }

            table::-webkit-scrollbar-thumb:hover {
                background: rgba(181, 20, 230, 0.7);
            }

            /* 性能优化 */
            * {
                -webkit-transform: translateZ(0);
                transform: translateZ(0);
            }
        }
    </style>
</head>
<body>
    <!-- Language Toggle Button -->
    <button type="button" class="language-toggle" onclick="toggleLanguage()" id="langToggle">
        <span class="lang-cn hidden">🌐 中/EN</span><span class="lang-en">🌐 中/EN</span>
    </button>

    <div class="navigation" onclick="toggleNavigation()">
        <div class="navigation-toggle">☰</div>
        <div class="navigation-content">
            <h4 class="lang-cn hidden">导航</h4>
            <h4 class="lang-en">Navigation</h4>
            <a href="#slide1" onclick="event.stopPropagation()" class="lang-cn hidden">封面</a>
            <a href="#slide1" onclick="event.stopPropagation()" class="lang-en">Cover</a>
            <a href="#slide2" onclick="event.stopPropagation()" class="lang-cn hidden">登录</a>
            <a href="#slide2" onclick="event.stopPropagation()" class="lang-en">Login</a>
            <a href="#slide3" onclick="event.stopPropagation()" class="lang-cn hidden">创建订单</a>
            <a href="#slide3" onclick="event.stopPropagation()" class="lang-en">Create Order</a>
            <a href="#slide4" onclick="event.stopPropagation()" class="lang-cn">按键功能</a>
            <a href="#slide4" onclick="event.stopPropagation()" class="lang-en">Button Guide</a>
            <a href="#slide5" onclick="event.stopPropagation()" class="lang-cn">价目表</a>
            <a href="#slide5" onclick="event.stopPropagation()" class="lang-en">Price List</a>
            <a href="#slide6" onclick="event.stopPropagation()" class="lang-cn">充值流程</a>
            <a href="#slide6" onclick="event.stopPropagation()" class="lang-en">Top-up</a>
            <a href="#slide7" onclick="event.stopPropagation()" class="lang-cn">常见问题</a>
            <a href="#slide7" onclick="event.stopPropagation()" class="lang-en">FAQ</a>
            <a href="#slide8" onclick="event.stopPropagation()" class="lang-cn">快捷链接</a>
            <a href="#slide8" onclick="event.stopPropagation()" class="lang-en">Quick Link</a>
            <a href="#slide9" onclick="event.stopPropagation()" class="lang-cn">优惠活动</a>
            <a href="#slide9" onclick="event.stopPropagation()" class="lang-en">Promotions</a>
            <a href="#slide10" onclick="event.stopPropagation()" class="lang-cn">联系我们</a>
            <a href="#slide10" onclick="event.stopPropagation()" class="lang-en">Contact</a>
        </div>
    </div>

    <div class="container">
        <!-- Slide 1: Cover -->
        <div id="slide1" class="slide cover-slide">
            <div class="slide-number"></div>
            <h1 class="lang-cn hidden">GoMyHire 合作伙伴信用额度指南</h1>
            <h1 class="lang-en">GoMyHire Partner Credit Limit Guide</h1>
            <div class="subtitle lang-cn hidden">欢迎使用本指南！</div>
            <div class="subtitle lang-en">Welcome to this guide!</div>
            <div class="date lang-cn hidden">日期: 2025年1月</div>
            <div class="date lang-en">Date: January 2025</div>
        </div>

        <!-- Slide 2: Login -->
        <div id="slide2" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">登录</h2>
            <h2 class="lang-en">Login</h2>
            
            <div class="step-container lang-cn hidden">
                <table>
                    <thead>
                        <tr>
                            <th>步骤</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1. 访问 https://gomyhire.com.my 网站首页</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="step-container lang-en">
                <table>
                    <thead>
                        <tr>
                            <th>Steps</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1. Visit https://gomyhire.com.my homepage</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="step-container lang-cn hidden">
                <table>
                    <tbody>
                        <tr>
                            <td>2. 点击页面右上角的「Login/ Sign Up」按钮</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/登录入口.jpeg" alt="登录页面截图" class="slide-image">
            </div>
            
            <div class="step-container lang-en">
                <table>
                    <tbody>
                        <tr>
                            <td>2. Click "Login/ Sign Up" button at top-right corner</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/登录入口.jpeg" alt="登录页面截图" class="slide-image">
            </div>

            <div class="step-container lang-cn">
                <table>
                    <tbody>
                        <tr>
                            <td>3. 在登录页面输入合作伙伴账号和密码</td>
                        </tr>
                        <tr>
                            <td>4. 点击「Sign In」按钮完成登录</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/登录界面.png" alt="登录界面截图" class="slide-image">
            </div>
            
            <div class="step-container lang-en">
                <table>
                    <tbody>
                        <tr>
                            <td>3. Enter partner account and password on login page</td>
                        </tr>
                        <tr>
                            <td>4. Click "Sign In" button to complete login</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/登录界面.png" alt="登录界面截图" class="slide-image">
            </div>

            <div class="step-container lang-cn">
                <table>
                    <tbody>
                        <tr>
                            <td>5. 登录成功后进入合作伙伴后台，可查看信用额度</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/余额.jpeg" alt="余额查询页面截图" class="slide-image">
            </div>
            
            <div class="step-container lang-en">
                <table>
                    <tbody>
                        <tr>
                            <td>5. After login, access partner dashboard to view credit limit</td>
                        </tr>
                    </tbody>
                </table>
                <img src="./images/余额.jpeg" alt="余额查询页面截图" class="slide-image">
            </div>
        </div>

        <!-- Slide 3: Create Order -->
        <div id="slide3" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn">创建订单</h2>
            <h2 class="lang-en">Create Order</h2>

            <img src="./images/创建订单表单.jpeg" alt="创建订单表单页面截图" class="slide-image with-margin-bottom">

            <h3>Field Table</h3>
            <div class="tip-box">
                <div class="lang-cn">
                    <strong>提交前请确认日期、时间及地点！</strong>
                </div>
                <div class="lang-en hidden">
                    <strong>Double-check date, time & location before submission!</strong>
                </div>
            </div>

            <div class="field-introduction">
                <h3 class="lang-cn hidden">重要字段说明</h3>
                <h3 class="lang-en">Key Field Guide</h3>
                <div class="field-section">
                    <div class="field-detail">
                        <strong class="lang-cn">- OTA参考号:</strong><strong class="lang-en hidden">- OTA Reference:</strong> <span class="lang-cn">必须唯一，多订单可添加-x序列号（如ABC123-1）</span><span class="lang-en hidden">Must be unique, add -x for multiple orders (e.g., ABC123-1)</span>
                    </div>
                    <div class="field-detail">
                        <strong class="lang-cn">- OTA价格:</strong><strong class="lang-en hidden">- OTA Price:</strong> <span class="lang-cn">必须与价目表一致，不在表内填写0</span><span class="lang-en hidden">Must match price list, enter 0 if not listed</span>
                    </div>
                    <div class="field-detail">
                        <strong class="lang-cn">- 客户联系方式:</strong><strong class="lang-en hidden">- Customer Contact:</strong> <span class="lang-cn">必须包含国际区号（如+60123456789）</span><span class="lang-en hidden">Must include international code (e.g., +60123456789)</span>
                    </div>
                    <div class="field-detail">
                        <strong class="lang-cn">- 地址选择:</strong><strong class="lang-en hidden">- Address Selection:</strong> <span class="lang-cn">必须从Google地图下拉列表中选择</span><span class="lang-en hidden">Must select from Google Maps dropdown</span>
                    </div>
                    <div class="field-detail">
                        <strong class="lang-cn">- 司机收费:</strong><strong class="lang-en hidden">- Driver Collect:</strong> <span class="lang-cn">即使客户拒付，费用仍从信用额度扣除</span><span class="lang-en hidden">Fee deducted from credit even if customer refuses</span>
                    </div>
                </div>
            </div>





            <div class="requirements-section">
                <div class="lang-cn">
                    <h3 class="lang-cn hidden">重要提醒</h3>
                <h3 class="lang-en">Important Reminders</h3>
                    
                    <div class="requirement-category">
                        <ul class="requirement-list">
                            <li><strong>⚠️ 提交前检查:</strong> 仔细核对日期、时间和地点</li>
                            <li><strong>📞 联系方式:</strong> 必须包含国际区号（+60123456789）</li>
                            <li><strong>💰 价格一致性:</strong> OTA价格必须与价目表一致，不在表内填写0</li>
                            <li><strong>🚗 车型匹配:</strong> 乘客人数不能超过车型载客量</li>
                            <li><strong>✈️ 送机时间:</strong> 需提前3.5小时预留缓冲时间</li>
                            <li><strong>💵 司机收费:</strong> 即使客户拒付，费用仍从信用额度扣除</li>
                        </ul>
                    </div>
                </div>
                
                <div class="lang-en hidden">
                    <div class="requirement-category">
                        <ul class="requirement-list">
                            <li><strong>⚠️ Pre-submission Check:</strong> Carefully verify dates, times, and locations</li>
                            <li><strong>📞 Contact Info:</strong> Must include international code (+60123456789)</li>
                            <li><strong>💰 Price Consistency:</strong> OTA prices must match price list, enter 0 if not listed</li>
                            <li><strong>🚗 Vehicle Matching:</strong> Passenger count cannot exceed vehicle capacity</li>
                            <li><strong>✈️ Airport Transfer:</strong> Requires 3.5 hours advance buffer time</li>
                            <li><strong>💵 Driver Fee:</strong> Fee deducted from credit limit even if customer refuses payment</li>
                        </ul>
                    </div>
                </div>
            </div>

        </div>

        <!-- Slide 4: Button Functions Guide -->
        <div id="slide4" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn">🎮 按键功能说明</h2>
            <h2 class="lang-en">🎮 Button Functions Guide</h2>
            
            <div class="field-introduction">
<img src="./images/订单列表.jpeg" alt="订单列表页面截图" class="slide-image with-margin-left">
                <div class="field-section">
                    <h3 class="lang-cn">📋 订单操作按键</h3>
                    <h3 class="lang-en">📋 Order Operation Buttons</h3>
                    <div class="lang-cn">
                        <div class="field-detail">
                            <strong>📝 Log:</strong> 查看订单详细日志记录，包括状态变更历史和操作记录
                        </div>
                    </div>
                    <div class="lang-en hidden">
                        <div class="field-detail">
                            <strong>📝 Log:</strong> View detailed order logs including status change history and operation records
                        </div>
                    </div>
                    <div class="lang-cn">
                        <div class="field-detail">
                            <strong>❌ Cancel Log:</strong> 取消订单并记录取消原因，支持批量取消操作
                        </div>
                    </div>
                    <div class="lang-en hidden">
                        <div class="field-detail">
                            <strong>❌ Cancel Log:</strong> Cancel order and record cancellation reason, supports batch cancellation
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>💰 Online Payment Link:</strong> 生成在线支付链接，方便客户完成付款
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>💰 Online Payment Link:</strong> Generate online payment link for customer convenience
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>💬 Copy Customer Chat Link:</strong> 复制客户聊天链接，便于直接沟通联系
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>💬 Copy Customer Chat Link:</strong> Copy customer chat link for direct communication
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>📧 Resend Email to Customer:</strong> 重新发送订单确认邮件给客户
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>📧 Resend Email to Customer:</strong> Resend order confirmation email to customer
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>✅ Resend Order Confirmation:</strong> 重新发送订单确认信息和详情
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>✅ Resend Order Confirmation:</strong> Resend order confirmation information and details
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>👥 Enter Group Chat:</strong> 进入订单相关的群组聊天室进行协调
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>👥 Enter Group Chat:</strong> Enter order-related group chat for coordination
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>🔍 View Order Detail:</strong> 查看订单完整详细信息和历史记录
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>🔍 View Order Detail:</strong> View complete order details and history
                        </div>
                    </div>
                </div>

                <div class="field-section">
                    <h3 class="lang-cn hidden">⚙️ 订单管理按键</h3>
                    <h3 class="lang-en">⚙️ Order Management Buttons</h3>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>✏️ Edit:</strong> 编辑订单信息（仅限特定状态下可用）
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>✏️ Edit:</strong> Edit order information (only available in specific status)
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>🗑️ Delete:</strong> 删除订单（仅限创建状态，谨慎操作）
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>🗑️ Delete:</strong> Delete order (only in created status, use with caution)
                        </div>
                    </div>
                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>📋 Copy Order:</strong> 复制订单信息创建新订单，保持格式一致
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>📋 Copy Order:</strong> Copy order information to create new order with consistent format
                        </div>
                    </div>

                    <div class="lang-cn hidden">
                        <div class="field-detail">
                            <strong>🔄 Double Check Order:</strong> 对订单进行二次核查确认信息准确
                        </div>
                    </div>
                    <div class="lang-en">
                        <div class="field-detail">
                            <strong>🔄 Double Check Order:</strong> Double check order to confirm information accuracy
                        </div>
                    </div>
                </div>

                <div class="tip-box lang-cn hidden">
                    <strong>💡 重要提示：</strong> 不同订单状态下可用的按键功能会有所不同，请根据订单当前状态选择合适的操作。某些操作（如删除、取消）不可撤销，请谨慎使用。
                </div>
                <div class="tip-box lang-en">
                    <strong>💡 Important:</strong> Available button functions vary depending on order status. Please select appropriate actions based on current order status. Some operations (like delete, cancel) are irreversible, use with caution.
                </div>
            </div>

        </div>

        <!-- Slide 5: OTA Settlement Price List -->
        <div id="slide5" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">OTA 结算价目表</h2>
            <h2 class="lang-en">OTA Settlement Price List</h2>
            

            <div class="price-table-container">
                <table class="settlement-table">
                    <caption class="lang-cn hidden">吉隆坡地区</caption>
                    <caption class="lang-en">Kuala Lumpur Area</caption>
                    <thead>
                        <tr>
                            <th class="lang-cn hidden">从吉隆坡机场出发</th>
                            <th class="lang-en">From Klia airport</th>
                            <th class="lang-cn hidden">轿车</th>
                            <th class="lang-en">Sedan</th>
                            <th class="lang-cn hidden">7座SUV</th>
                            <th class="lang-en">7 seater suv</th>
                            <th class="lang-cn hidden">标准MPV</th>
                            <th class="lang-en">standard mpv</th>
                            <th class="lang-cn hidden">豪华MPV (Serena)</th>
                            <th class="lang-en">luxury mpv (serena)</th>
                            <th class="lang-cn hidden">阿尔法德 (高端MPV)</th>
                            <th class="lang-en">alphard (premium mpv)</th>
                            <th class="lang-cn hidden">10座</th>
                            <th class="lang-en">10 Seater</th>
                            <th class="lang-cn hidden">18座面包车</th>
                            <th class="lang-en">18 Seater van</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td><span class="lang-cn hidden">吉隆坡市区</span><span class="lang-en">kl City</span></td><td>70</td><td>85</td><td>100</td><td>130</td><td>190</td><td>200</td><td>235</td></tr>
                        <tr><td><span class="lang-cn hidden">云顶</span><span class="lang-en">Genting</span></td><td>170</td><td>210</td><td>265</td><td>320</td><td>370</td><td>360</td><td>400</td></tr>
                        <tr><td><span class="lang-cn hidden">马六甲</span><span class="lang-en">melaka</span></td><td>180</td><td>220</td><td>275</td><td>320</td><td>390</td><td>380</td><td>420</td></tr>
                        <tr><td><span class="lang-cn hidden">新山</span><span class="lang-en">JB</span></td><td>600</td><td>660</td><td>780</td><td>850</td><td>1000</td><td>950</td><td>1050</td></tr>
                        <tr><td><span class="lang-cn hidden">槟城</span><span class="lang-en">penang</span></td><td>700</td><td>780</td><td>880</td><td>1050</td><td>1300</td><td>1250</td><td>1360</td></tr>
                        <tr><td><span class="lang-cn hidden">红土坎</span><span class="lang-en">Lumut</span></td><td>600</td><td>660</td><td>780</td><td>850</td><td>1000</td><td>950</td><td>1050</td></tr>
                    </tbody>
                </table>
                <table class="settlement-table">
                    <caption class="lang-cn hidden">包车服务 (从吉隆坡市区出发)</caption>
                    <caption class="lang-en">Charter (From KL City)</caption>
                    <thead>
                        <tr>
                            <th class="lang-cn hidden">服务类型</th>
                            <th class="lang-en">Service Type</th>
                            <th class="lang-cn hidden">轿车</th>
                            <th class="lang-en">Sedan</th>
                            <th class="lang-cn hidden">7座SUV</th>
                            <th class="lang-en">7 seater suv</th>
                            <th class="lang-cn hidden">标准MPV</th>
                            <th class="lang-en">standard mpv</th>
                            <th class="lang-cn hidden">豪华MPV (Serena)</th>
                            <th class="lang-en">luxury mpv (serena)</th>
                            <th class="lang-cn hidden">阿尔法德 (高端MPV)</th>
                            <th class="lang-en">alphard (premium mpv)</th>
                            <th class="lang-cn hidden">10座</th>
                            <th class="lang-en">10 Seater</th>
                            <th class="lang-cn hidden">18座面包车</th>
                            <th class="lang-en">18 Seater van</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td><span class="lang-cn hidden">吉隆坡 5小时</span><span class="lang-en">kl 5 hour</span></td><td>200</td><td>240</td><td>300</td><td>360</td><td>500</td><td>480</td><td>650</td></tr>
                        <tr><td><span class="lang-cn hidden">吉隆坡 10小时</span><span class="lang-en">kl 10hour</span></td><td>300</td><td>360</td><td>440</td><td>550</td><td>850</td><td>800</td><td>880</td></tr>
                        <tr><td><span class="lang-cn hidden">布城马六甲 10小时</span><span class="lang-en">putrajaya melaka 10 hour</span></td><td>400</td><td>460</td><td>550</td><td>650</td><td>850</td><td>900</td><td>1350</td></tr>
                        <tr><td><span class="lang-cn hidden">云顶 10小时</span><span class="lang-en">Genting 10hour</span></td><td>360</td><td>480</td><td>600</td><td>680</td><td>800</td><td>850</td><td>1250</td></tr>
                        <tr><td><span class="lang-cn hidden">瓜拉雪兰莪 6小时</span><span class="lang-en">kuala selangor 6 hour</span></td><td>300</td><td>360</td><td>440</td><td>550</td><td>850</td><td>800</td><td>880</td></tr>
                        <tr><td><span class="lang-cn hidden">瓜拉雪兰莪 12小时</span><span class="lang-en">kuala selangor 12 hour</span></td><td>600</td><td>680</td><td>800</td><td>900</td><td>1150</td><td>1050</td><td>1250</td></tr>
                        <tr><td><span class="lang-cn hidden">怡保</span><span class="lang-en">ipoh</span></td><td>650</td><td>750</td><td>850</td><td>1000</td><td>1250</td><td>1200</td><td>1350</td></tr>
                    </tbody>
                </table>
                <table class="settlement-table">
                    <caption class="lang-cn hidden">从吉隆坡市区单程</caption>
                    <caption class="lang-en">From KL City single way</caption>
                    <thead>
                        <tr>
                            <th class="lang-cn hidden">目的地</th>
                            <th class="lang-en">Destination</th>
                            <th class="lang-cn hidden">轿车</th>
                            <th class="lang-en">Sedan</th>
                            <th class="lang-cn hidden">7座SUV</th>
                            <th class="lang-en">7 seater suv</th>
                            <th class="lang-cn hidden">标准MPV</th>
                            <th class="lang-en">standard mpv</th>
                            <th class="lang-cn hidden">豪华MPV (Serena)</th>
                            <th class="lang-en">luxury mpv (serena)</th>
                            <th class="lang-cn hidden">阿尔法德 (高端MPV)</th>
                            <th class="lang-en">alphard (premium mpv)</th>
                            <th class="lang-cn hidden">10座</th>
                            <th class="lang-en">10 Seater</th>
                            <th class="lang-cn hidden">18座面包车</th>
                            <th class="lang-en">18 Seater van</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td><span class="lang-cn hidden">马六甲</span><span class="lang-en">melaka</span></td><td>250</td><td>300</td><td>400</td><td>500</td><td>800</td><td>850</td><td>900</td></tr>
                        <tr><td><span class="lang-cn hidden">云顶</span><span class="lang-en">genting</span></td><td>100</td><td>130</td><td>180</td><td>240</td><td>300</td><td>310</td><td>450</td></tr>
                        <tr><td><span class="lang-cn hidden">红土坎</span><span class="lang-en">lumut</span></td><td>600</td><td>660</td><td>780</td><td>850</td><td>1000</td><td>950</td><td>1050</td></tr>
                        <tr><td><span class="lang-cn hidden">新加坡</span><span class="lang-en">singapore</span></td><td>800</td><td>1000</td><td>1200</td><td>1450</td><td>1600</td><td>1550</td><td>1750</td></tr>
                        <tr><td><span class="lang-cn hidden">槟城</span><span class="lang-en">penang</span></td><td>700</td><td>780</td><td>880</td><td>1050</td><td>1300</td><td>1250</td><td>1360</td></tr>
                        <tr><td><span class="lang-cn hidden">新山</span><span class="lang-en">jb</span></td><td>600</td><td>660</td><td>780</td><td>850</td><td>1000</td><td>950</td><td>1050</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="price-warning-section">
                <h3 class="lang-cn hidden">旺季与高峰期价格</h3>
                <h3 class="lang-en">Peak Season & High Demand Prices</h3>
                <ul class="step-list">
                    <li class="lang-cn hidden"><strong>📊 价格参考：</strong> 请使用 WhatsApp 群组中分享的最新 OTA 报价 Excel 文件</li>
                    <li class="lang-en"><strong>📊 Price Reference:</strong> Please use the latest OTA quotation Excel file shared in the WhatsApp group</li>
                    <li class="lang-cn hidden"><strong>✅ 可接：</strong> 高峰期订单</li>
                    <li class="lang-en"><strong>✅ Acceptable:</strong> High demand period orders</li>
                    <li class="lang-cn hidden"><strong>❌ 禁止：</strong> 无在 OTA 价目表中的价格</li>
                    <li class="lang-en"><strong>❌ Prohibited:</strong> Prices not listed in the OTA price list</li>
                </ul>
                
                <h3 class="lang-cn hidden">重要规定</h3>
                <h3 class="lang-en">Important Regulations</h3>
                <ul class="step-list">
                    <li class="lang-cn hidden"><strong>💰 OTA价格：</strong> 输入的OTA价格为实际扣除的信用额度</li>
                    <li class="lang-en"><strong>💰 OTA Price:</strong> The entered OTA price is the actual credit amount to be deducted</li>
                    <li class="lang-cn hidden"><strong>🗑️ 订单删除规则：</strong> 未推送到订单池之前可自由删除订单；订单一旦进入订单池则需要客服协助取消/删除</li>
                    <li class="lang-en"><strong>🗑️ Order Deletion Rules:</strong> Orders can be freely deleted before being pushed to the order pool; once orders enter the pool, customer service assistance is required for cancellation/deletion</li>
                    <li class="lang-cn hidden"><strong>⏰ 提前24小时内取消订单：</strong> 收取50%费用</li>
                    <li class="lang-en"><strong>⏰ Cancellation within 24 hours:</strong> 50% fee charged</li>
                    <li class="lang-cn hidden"><strong>📅 到达当天取消：</strong> 收取100%费用</li>
                    <li class="lang-en"><strong>📅 Same-day cancellation:</strong> 100% fee charged</li>
                    <li class="lang-cn hidden"><strong>💰 价格包含：</strong> 司机与油费，无其他隐藏费用</li>
                    <li class="lang-en"><strong>💰 Price includes:</strong> Driver and fuel costs, no hidden fees</li>
                </ul>
            </div>
            
            <ul class="step-list">
                <li class="lang-cn hidden"><strong>提示：</strong> 订单内 "OTA Price" 必须与此表一致。</li>
                <li class="lang-en"><strong>Tips:</strong> The "OTA Price" in the order must be consistent with this table.</li>
                <li class="lang-cn hidden"><strong>注意：</strong> 价格按车型与里程分级，季节更新。</li>
                <li class="lang-en"><strong>Note:</strong> Prices are graded by car model and mileage, and are updated seasonally.</li>
            </ul>
        </div>

        <!-- Slide 6: Top-up Flow -->
        <div id="slide6" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">💳 充值流程</h2>
        <h2 class="lang-en">💳 Top-up Flow</h2>
            
            <table>
                <thead>
                    <tr>
                        <th class="lang-cn hidden">步骤</th>
            <th class="lang-en">Step</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="lang-cn hidden">1. 点击侧边栏「Operator」，选择二级菜单「Operator Credit」</td>
                        <td class="lang-en">1. Click "Operator" on sidebar, then select "Operator Credit" from submenu</td>
                    </tr>
                    <tr>
                        <td class="lang-cn hidden">2. 输入金额 Amount（RM）</td>
                        <td class="lang-en">2. Input Amount (RM)</td>
                    </tr>
                    <tr>
                        <td class="lang-cn hidden">3. 上传转账凭证并点击「提交」</td>
                        <td class="lang-en">3. Upload transaction slip and click "Submit"</td>
                    </tr>
                    <tr>
                        <td class="lang-cn hidden">4. 上传后请通知客服做额度更新</td>
                        <td class="lang-en">4. Please notify customer service to update credit limit after upload</td>
                    </tr>
                </tbody>
            </table>

            <div class="price-warning-section">
                <h3 class="lang-cn hidden">退款政策</h3>
        <h3 class="lang-en">Refund Policy</h3>
                <ul class="step-list">
                    <li class="lang-cn hidden"><strong>🔁 退款冷静期：</strong> 3个月</li>
                    <li class="lang-en"><strong>🔁 Refund cooling-off period:</strong> 3 months</li>
                    <li class="lang-cn hidden"><strong>❗ 赠送的免费额度不退还</strong></li>
                    <li class="lang-en"><strong>❗ Gifted free credits are non-refundable</strong></li>
                </ul>
            </div>

            <img src="./images/充值.jpeg" alt="充值页面截图" class="slide-image">
        </div>

        <!-- Slide 7: FAQ -->
        <div id="slide7" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">❓ 常见问题</h2>
        <h2 class="lang-en">❓ FAQ</h2>
            
            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：如果我找不到价钱怎么办？</div>
            <div class="faq-question lang-en">Q: What if I can't find the price?</div>
            <div class="faq-answer lang-cn hidden">🅰 请在 WhatsApp 群组中向我们确认</div>
            <div class="faq-answer lang-en">🅰 Please confirm with us in the WhatsApp group</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：如果我输入错误或没有填写 OTA 价格怎么办？</div>
            <div class="faq-question lang-en">Q: What if I input the wrong price or don't fill in the OTA price?</div>
            <div class="faq-answer lang-cn hidden">🅰 调度会提醒您订单的状态变更，请保持关注</div>
            <div class="faq-answer lang-en">🅰 Dispatch will notify you of order status changes, please stay tuned</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：我的信用额度何时会被扣除？</div>
            <div class="faq-question lang-en">Q: When will my credit limit be deducted?</div>
            <div class="faq-answer lang-cn hidden">🅰 订单被审核通过并指派司机后即会扣除信用</div>
            <div class="faq-answer lang-en">🅰 Credit will be deducted after the order is approved and a driver is assigned</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：如何查看每个订单扣了多少额度？</div>
            <div class="faq-question lang-en">Q: How can I check how much credit was deducted for each order?</div>
            <div class="faq-answer lang-cn hidden">🅰 可在系统中的「订单管理」页面查看每笔订单的OTA价格，或在「信用记录」页面查看历史记录</div>
            <div class="faq-answer lang-en">🅰 You can view the OTA price for each order in the "Order Management" page, or check the history in the "Credit Record" page</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：订单取消的时间是如何计算的？</div>
            <div class="faq-question lang-en">Q: How is the order cancellation time calculated?</div>
            <div class="faq-answer lang-cn hidden">🅰 订单创建后会进入创建订单池，之后调度会将订单推送到抢单池。订单在进入抢单池之前可以自由取消删除；一旦进入抢单池则需要客服协助取消/删除。订单取消费用按当前时间与订单时间的差值计算。</div>
            <div class="faq-answer lang-en">🅰 After creation, orders enter the creation pool, then dispatch will push orders to the bidding pool. Orders can be freely cancelled/deleted before entering the bidding pool; once in the bidding pool, customer service assistance is required. Cancellation fees are calculated based on the time difference between current time and order time.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：OTA价格与实际扣除的额度是否一致？</div>
            <div class="faq-question lang-en">Q: Is the OTA price consistent with the actual amount deducted?</div>
            <div class="faq-answer lang-cn hidden">🅰 是的，输入的OTA价格即为实际扣除的信用额度，请确保价格准确。</div>
            <div class="faq-answer lang-en">🅰 Yes, the entered OTA price is the actual credit amount to be deducted, so please ensure the price is accurate.</div>
            </div>

            <div class="faq-item">
                <div class="faq-question lang-cn hidden">Q：如果我想取消订单，如何操作？</div>
            <div class="faq-question lang-en">Q: How do I cancel an order if I want to?</div>
            <div class="faq-answer lang-cn hidden">🅰 可通过系统取消或通知群组管理员（依取消政策收费）</div>
            <div class="faq-answer lang-en">🅰 You can cancel through the system or notify the group admin (subject to cancellation policy charges)</div>
            </div>

        </div>



        <!-- Slide 8: Smart Order Link -->
        <div id="slide8" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">🔗 智能订单链接</h2>
        <h2 class="lang-en">🔗 Smart Order Link</h2>
            
            <ul class="step-list">
                <li class="lang-cn hidden">🔗 <strong>智能进单：</strong> 合作伙伴填写订单后直接进入GoMyHire后台订单创建池</li>
            <li class="lang-en">🔗 <strong>Smart Order:</strong> After partner fills the order, it goes directly into GoMyHire backend order creation pool</li>
            <li class="lang-cn hidden">📋 <strong>链接地址：</strong> <code>https://createjobgmh.netlify.app/</code></li>
            <li class="lang-en">📋 <strong>Link Address:</strong> <code>https://createjobgmh.netlify.app/</code></li>
            <li class="lang-cn hidden">将任意格式的订单内容粘贴到文本框中，系统将自动识别并提取关键信息（如客人信息、入住日期等），生成标准订单并同步至GoMyHire后台</li>
            <li class="lang-en">Paste order content in any format into the textbox, the system will automatically identify and extract key information (such as guest details, check-in dates, etc.), generate a standardized order and sync to GoMyHire backend</li>
            </ul>

            <img src="./images/智能进单.png" alt="智能进单页面截图" class="slide-image">
        </div>

        <!-- Slide 9: Promotions -->
        <div id="slide9" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">🎉 优惠活动</h2>
        <h2 class="lang-en">🎉 Promotions</h2>
            
            <table class="promo-table">
                <thead>
                    <tr>
                        <th class="lang-cn hidden">活动</th>
                        <th class="lang-en">Promo</th>
                        <th class="lang-cn hidden">代码</th>
                        <th class="lang-en">Code</th>
                        <th class="lang-cn hidden">详情</th>
                        <th class="lang-en">Details</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="lang-cn hidden">首单 95 折</td>
                        <td class="lang-en">5% off first order</td>
                        <td><strong>FIRST95</strong></td>
                        <td class="lang-cn hidden">新客户首单专享</td>
                        <td class="lang-en">Exclusive for new customers' first order</td>
                    </tr>
                    <tr>
                        <td class="lang-cn hidden">月租满 30 天送 2 天</td>
                        <td class="lang-en">Rent for 30 days, get 2 days free</td>
                        <td><strong>LONG30</strong></td>
                        <td class="lang-cn hidden">长期租赁奖励</td>
                        <td class="lang-en">Long-term lease reward</td>
                    </tr>
                    <tr>
                        <td class="lang-cn hidden">当月总额 > MYR 50K 返现 2%</td>
                        <td class="lang-en">Monthly total > MYR 50K, 2% cashback</td>
                        <td><strong>CASHBACK2</strong></td>
                        <td class="lang-cn hidden">自动返现</td>
                        <td class="lang-en">Automatic cashback</td>
                    </tr>
                </tbody>
            </table>

        </div>

        <!-- Slide 10: Contact Us -->
        <div id="slide10" class="slide">
            <div class="slide-number"></div>
            <h2 class="lang-cn hidden">📞 联系我们</h2>
            <h2 class="lang-en">📞 Contact Us</h2>
            
            <div class="contact-info">
                <div class="contact-card">
                    <h4 class="lang-cn hidden">运营支持</h4>
                    <h4 class="lang-en">Ops Support</h4>
                    <p><EMAIL></p>
                </div>
                <div class="contact-card">
                    <h4 class="lang-cn hidden">财务团队</h4>
                    <h4 class="lang-en">Finance</h4>
                    <p><EMAIL></p>
                </div>
                <div class="contact-card">
                    <h4 class="lang-cn hidden">热线</h4>
                    <h4 class="lang-en">Hotline</h4>
                    <p>+603-78900670</p>
                    <p>(Mon-Sun 09:00-22:00)</p>
                </div>
            </div>


            <div class="text-center-highlight">
                <span class="lang-cn hidden">感谢使用 GoMyHire，祝业务蒸蒸日上！</span>
                <span class="lang-en">Thank you for choosing GoMyHire, wish you great success!</span>
            </div>
        </div>

    <script>
        // Smooth scroll to the specified slide
        document.querySelectorAll('.navigation a').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            });
        });

        // Add visual effects on scroll
        window.addEventListener('scroll', () => {
            const slides = document.querySelectorAll('.slide');
            slides.forEach(slide => {
                const rect = slide.getBoundingClientRect();
                const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
                
                if (isVisible) {
                    slide.style.opacity = '1';
                    slide.style.transform = 'translateY(0)';
                } else {
                    slide.style.opacity = '0.7';
                    slide.style.transform = 'translateY(20px)';
                }
            });
        });

        // Initialize animation
        document.addEventListener('DOMContentLoaded', () => {
            const slides = document.querySelectorAll('.slide');
            slides.forEach((slide, index) => {
                slide.style.transition = 'all 0.6s ease';
                slide.style.opacity = index === 0 ? '1' : '0.7';
                slide.style.transform = index === 0 ? 'translateY(0)' : 'translateY(20px)';
            });
        });



        // Navigation toggle functionality
        function toggleNavigation() {
            const navigation = document.querySelector('.navigation');
            navigation.classList.toggle('expanded');
            
            // Update toggle icon based on state
            const toggle = navigation.querySelector('.navigation-toggle');
            if (navigation.classList.contains('expanded')) {
                toggle.textContent = '✕';
            } else {
                toggle.textContent = '☰';
            }
        }

        // Language toggle functionality
        let isEnglishVisible = true; // Default to show English

        function setLanguage(isEnglish) {
            const cnElements = document.querySelectorAll('.lang-cn');
            const enElements = document.querySelectorAll('.lang-en');
            const toggleButton = document.getElementById('langToggle');

            if (isEnglish) {
                cnElements.forEach(el => el.classList.add('hidden'));
                enElements.forEach(el => el.classList.remove('hidden'));
                toggleButton.innerHTML = '🌐 中/EN';
                toggleButton.title = '切换到中文';
            } else {
                cnElements.forEach(el => el.classList.remove('hidden'));
                enElements.forEach(el => el.classList.add('hidden'));
                toggleButton.innerHTML = '🌐 中文';
                toggleButton.title = 'Switch to English';
            }
            isEnglishVisible = isEnglish;
        }

        function toggleLanguage() {
            setLanguage(!isEnglishVisible);
        }

        // Initialize language on page load
        document.addEventListener('DOMContentLoaded', () => {
            setLanguage(true); // Set default to English

            // 方案一：移动端优化初始化
            initMobileOptimizations();

            // 移动端优化：防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            
            // 优化触摸滚动
            document.addEventListener('touchmove', function(e) {
                if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                }
            }, { passive: false });
            
            // 移动端导航优化
            if (window.innerWidth <= 768) {
                const navigation = document.querySelector('.navigation');
                const slides = document.querySelectorAll('.slide');
                
                // 自动隐藏导航栏（滚动时）
                let lastScrollTop = 0;
                window.addEventListener('scroll', () => {
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    if (scrollTop > lastScrollTop && scrollTop > 100) {
                        navigation.style.opacity = '0.5';
                    } else {
                        navigation.style.opacity = '1';
                    }
                    lastScrollTop = scrollTop;
                });
                
                // 触摸优化
                slides.forEach(slide => {
                    slide.style.touchAction = 'pan-y';
                });
            }
            
            // 图片懒加载优化
            const images = document.querySelectorAll('img');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        // 检查图片是否已经加载完成
                        if (img.complete && img.naturalHeight !== 0) {
                            // 图片已加载，直接显示
                            img.style.opacity = '1';
                            img.style.transition = 'opacity 0.3s ease';
                        } else {
                            // 图片未加载，设置加载动画
                            img.style.opacity = '0';
                            img.style.transition = 'opacity 0.3s ease';
                            
                            const handleLoad = () => {
                                img.style.opacity = '1';
                                img.removeEventListener('load', handleLoad);
                                img.removeEventListener('error', handleError);
                            };
                            
                            const handleError = () => {
                                img.style.opacity = '0.5';
                                img.removeEventListener('load', handleLoad);
                                img.removeEventListener('error', handleError);
                            };
                            
                            img.addEventListener('load', handleLoad);
                            img.addEventListener('error', handleError);
                        }
                        
                        observer.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => {
                imageObserver.observe(img);
            });

            // 方案一：添加表格滚动提示
            if (window.innerWidth <= 768) {
                addTableScrollHints();
                optimizeTableScroll();

                // 强制修复所有表格的表头背景色
                fixAllTableHeaders();
            }
        });

        // 方案一：表格滚动提示功能
        function addTableScrollHints() {
            const tables = document.querySelectorAll('.settlement-table');
            tables.forEach(table => {
                // 修复表头背景色显示问题
                fixTableHeaderBackground(table);

                const hint = document.createElement('div');
                hint.className = 'table-scroll-hint';
                hint.innerHTML = '<span class="lang-cn hidden">👈 左右滑动查看更多数据</span><span class="lang-en">👈 Swipe left/right for more data</span>';
                hint.style.cssText = `
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    margin: 5px 0;
                    opacity: 0.8;
                    padding: 5px;
                    background: rgba(181, 20, 230, 0.1);
                    border-radius: 4px;
                `;
                table.parentNode.insertBefore(hint, table.nextSibling);
            });
        }

        // 修复表头背景色显示问题
        function fixTableHeaderBackground(table) {
            const headers = table.querySelectorAll('th');
            headers.forEach(th => {
                // 强制设置表头背景色
                th.style.background = 'linear-gradient(135deg, #b514e6cf, #8a0db8cf)';
                th.style.backgroundSize = '100% 100%';
                th.style.backgroundRepeat = 'no-repeat';
                th.style.backgroundAttachment = 'scroll';
                th.style.color = 'white';

                // 添加背景色保护层
                if (!th.querySelector('.bg-protection')) {
                    const bgProtection = document.createElement('div');
                    bgProtection.className = 'bg-protection';
                    bgProtection.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(135deg, #b514e6cf, #8a0db8cf);
                        z-index: -1;
                        pointer-events: none;
                    `;
                    th.style.position = 'relative';
                    th.appendChild(bgProtection);
                }
            });

            // 特殊处理促销表格
            if (table.classList.contains('promo-table')) {
                headers.forEach(th => {
                    th.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                    const bgProtection = th.querySelector('.bg-protection');
                    if (bgProtection) {
                        bgProtection.style.background = 'linear-gradient(135deg, #4caf50, #388e3c)';
                    }
                });
            }
        }

        // 强制修复所有表格的表头背景色
        function fixAllTableHeaders() {
            const allTables = document.querySelectorAll('table');
            allTables.forEach(table => {
                const headers = table.querySelectorAll('th');
                if (headers.length > 0) {
                    // 确定表格类型并应用相应的背景色
                    let bgGradient = 'linear-gradient(135deg, #b514e6cf, #8a0db8cf)'; // 默认紫色

                    if (table.classList.contains('promo-table')) {
                        bgGradient = 'linear-gradient(135deg, #4caf50, #388e3c)'; // 绿色
                    }

                    headers.forEach(th => {
                        // 强制应用背景色
                        th.style.setProperty('background', bgGradient, 'important');
                        th.style.setProperty('background-size', '100% 100%', 'important');
                        th.style.setProperty('background-repeat', 'no-repeat', 'important');
                        th.style.setProperty('background-attachment', 'scroll', 'important');
                        th.style.setProperty('color', 'white', 'important');

                        // 添加额外的保护措施
                        th.addEventListener('DOMNodeInserted', function() {
                            this.style.setProperty('background', bgGradient, 'important');
                        });
                    });
                }
            });

            // 监听窗口大小变化，重新应用修复
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    if (window.innerWidth <= 768) {
                        fixAllTableHeaders();
                    }
                }, 100);
            });
        }

        // 方案一：优化表格滚动体验
        function optimizeTableScroll() {
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                // 添加滚动指示器
                table.addEventListener('scroll', function() {
                    const scrollLeft = this.scrollLeft;
                    const scrollWidth = this.scrollWidth;
                    const clientWidth = this.clientWidth;

                    // 动态调整阴影
                    let boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                    if (scrollLeft > 0) {
                        boxShadow += ', inset 10px 0 10px -10px rgba(0,0,0,0.2)';
                    }
                    if (scrollLeft < scrollWidth - clientWidth) {
                        boxShadow += ', inset -10px 0 10px -10px rgba(0,0,0,0.2)';
                    }
                    this.style.boxShadow = boxShadow;
                });

                // 初始化阴影
                const scrollWidth = table.scrollWidth;
                const clientWidth = table.clientWidth;
                if (scrollWidth > clientWidth) {
                    table.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1), inset -10px 0 10px -10px rgba(0,0,0,0.2)';
                }
            });
        }

        // 方案一：移动端优化主函数
        function initMobileOptimizations() {
            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                // 添加表格滚动提示
                addTableScrollHints();

                // 优化表格滚动
                optimizeTableScroll();

                // 添加触摸反馈
                addTouchFeedback();

                // 优化导航体验
                optimizeMobileNavigation();

                // 添加移动端样式类
                document.body.classList.add('mobile-optimized');
            }
        }

        // 方案一：添加触摸反馈
        function addTouchFeedback() {
            const interactiveElements = document.querySelectorAll('button, .navigation a, .faq-item, .contact-card');

            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', function(e) {
                    this.style.transition = 'transform 0.1s ease, opacity 0.1s ease';
                    this.style.transform = 'scale(0.98)';
                    this.style.opacity = '0.9';
                }, { passive: true });

                element.addEventListener('touchend', function(e) {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                }, { passive: true });

                element.addEventListener('touchcancel', function(e) {
                    this.style.transform = 'scale(1)';
                    this.style.opacity = '1';
                }, { passive: true });
            });
        }

        // 方案一：优化移动端导航
        function optimizeMobileNavigation() {
            const navigation = document.querySelector('.navigation');
            if (!navigation) return;

            // 添加快速访问功能
            let hideTimeout;

            // 滚动时自动隐藏导航
            window.addEventListener('scroll', () => {
                navigation.style.opacity = '0.6';

                clearTimeout(hideTimeout);
                hideTimeout = setTimeout(() => {
                    navigation.style.opacity = '1';
                }, 1000);
            }, { passive: true });

            // 点击导航后自动收起
            navigation.addEventListener('click', (e) => {
                if (e.target.tagName === 'A') {
                    setTimeout(() => {
                        navigation.classList.remove('expanded');
                        const toggle = navigation.querySelector('.navigation-toggle');
                        if (toggle) toggle.textContent = '☰';
                    }, 300);
                }
            });
        }
    </script>
</body>
</html>