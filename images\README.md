# 图片资源说明 / Image Resources

## 📁 需要的图片文件 / Required Image Files

请将以下图片文件放置在此 `images/` 目录中：

### 登录相关图片 / Login Related Images
- `登录入口.jpeg` - 登录入口页面截图
- `登录界面.png` - 登录界面截图  
- `余额.jpeg` - 余额查询页面截图

### 订单相关图片 / Order Related Images
- `创建订单表单.jpeg` - 创建订单表单页面截图
- `订单列表.jpeg` - 订单列表页面截图

### 功能相关图片 / Feature Related Images
- `充值.jpeg` - 充值页面截图
- `智能进单.png` - 智能进单页面截图

## 🔧 图片优化建议 / Image Optimization Recommendations

### 文件格式 / File Formats
- 推荐使用 WebP 格式以获得更好的压缩率
- 保留原始 JPEG/PNG 格式作为降级方案

### 文件大小 / File Size
- 建议单个图片文件不超过 500KB
- 移动端显示的图片建议宽度不超过 800px

### 响应式图片 / Responsive Images
可以创建不同尺寸的图片版本：
- `图片名-mobile.webp` (移动端版本，宽度 ≤ 480px)
- `图片名-tablet.webp` (平板版本，宽度 ≤ 768px)  
- `图片名-desktop.webp` (桌面版本，宽度 > 768px)

## 📱 移动端优化 / Mobile Optimization

所有图片已经配置为：
- 自动适应容器宽度 (`max-width: 100%`)
- 保持宽高比 (`height: auto`)
- 懒加载支持 (Intersection Observer)
- 平滑过渡效果 (`transition: opacity 0.3s ease`)

## 🚀 部署说明 / Deployment Notes

1. 确保所有图片文件都放在 `images/` 目录中
2. 图片文件名必须与 HTML 中的引用完全一致
3. 支持中文文件名，但建议使用英文文件名以获得更好的兼容性

## ⚠️ 注意事项 / Important Notes

- 图片路径已从绝对路径修改为相对路径 (`./images/`)
- 这样可以避免 CORS 问题并支持静态部署
- 如果图片加载失败，会显示半透明状态和错误提示
